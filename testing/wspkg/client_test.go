package wspkg_test

import (
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/stretchr/testify/assert"
)

// MockWebSocketConn is a mock implementation of websocket.Conn for testing
type MockWebSocketConn struct {
	closed bool
}

func (m *MockWebSocketConn) Close() error {
	m.closed = true
	return nil
}

func TestClient_Creation(t *testing.T) {
	// Since NewClient requires a real websocket.Conn, we'll test the client behavior
	// by creating a client with a mock connection (this would need modification to the actual code)

	// For now, we'll test the client interface and behavior patterns
	// In a real implementation, you'd need to modify NewClient to accept an interface
	// or create a test-specific constructor

	// Test that we can create a message
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	assert.Equal(t, "test-event", message.GetEvent())
	assert.Equal(t, []byte(`{"data": "test"}`), message.GetData())
}

func TestMessage_Creation(t *testing.T) {
	// Test NewMessage
	event := "user-action"
	data := []byte(`{"action": "click", "element": "button"}`)

	message := wspkg.NewMessage(event, data)

	assert.Equal(t, event, message.Event)
	assert.Equal(t, data, []byte(message.Data))
	assert.Equal(t, event, message.GetEvent())
	assert.Equal(t, data, message.GetData())
}

func TestMessage_FromBytes(t *testing.T) {
	// Test NewMessageFromBytes with valid JSON
	validJSON := []byte(`{"event": "test-event", "data": {"key": "value"}}`)

	message, err := wspkg.NewMessageFromBytes(validJSON)

	assert.NoError(t, err)
	assert.Equal(t, "test-event", message.Event)
	assert.Equal(t, []byte(`{"key": "value"}`), []byte(message.Data))
}

func TestMessage_FromBytes_InvalidJSON(t *testing.T) {
	// Test NewMessageFromBytes with invalid JSON
	invalidJSON := []byte(`{"event": "test-event", "data": invalid}`)

	message, err := wspkg.NewMessageFromBytes(invalidJSON)

	assert.Error(t, err)
	assert.Nil(t, message)
}

func TestMessage_JSONBind(t *testing.T) {
	// Test binding message data to a struct using Ctx.Bind
	type TestData struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}

	data := []byte(`{"name": "test", "value": 42}`)
	message := wspkg.NewMessage("test", data)

	// Create a context to use the Bind method
	ctx := wspkg.Ctx{Data: message}

	var result TestData
	err := ctx.Bind(&result)

	assert.NoError(t, err)
	assert.Equal(t, "test", result.Name)
	assert.Equal(t, 42, result.Value)
}

func TestMessage_JSONBind_InvalidData(t *testing.T) {
	// Test binding with invalid JSON data
	data := []byte(`invalid json`)
	message := wspkg.NewMessage("test", data)

	// Create a context to use the Bind method
	ctx := wspkg.Ctx{Data: message}

	var result map[string]interface{}
	err := ctx.Bind(&result)

	assert.Error(t, err)
}

// Test client state management structures
func TestClientState_Creation(t *testing.T) {
	clientState := wspkg.NewClientState()

	assert.NotNil(t, clientState)
	assert.NotNil(t, clientState.DoorLocks)
	assert.NotNil(t, clientState.QRDevice)
	assert.Equal(t, 0, len(clientState.DoorLocks))
	assert.Equal(t, 0, len(clientState.QRDevice))
}

func TestClientState_DoorClient(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Test creating a door client
	clientID := "test-client-1"
	doorID := int64(100)
	name := "Main Entrance"
	branchID := 1

	clientState.CreateDoorClient(clientID, doorID, name, branchID)

	// Test getting the door client
	doorClient, err := clientState.GetDoorClient(clientID)
	assert.NoError(t, err)
	assert.NotNil(t, doorClient)
	assert.Equal(t, doorID, doorClient.DoorId)
	assert.Equal(t, name, doorClient.Name)
	assert.Equal(t, branchID, doorClient.BranchId)
	assert.False(t, doorClient.IsOpen())
	assert.False(t, doorClient.CheckIsProcessing())
}

func TestClientState_DoorClient_NotFound(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Test getting non-existent door client
	doorClient, err := clientState.GetDoorClient("non-existent")
	assert.Error(t, err)
	assert.Nil(t, doorClient)
	assert.Equal(t, "door client not found", err.Error())
}

func TestClientState_QRClient(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Test creating a QR client
	clientID := "qr-client-1"
	typ := "in"
	branchID := 1
	door := 100

	clientState.CreateQRClient(clientID, typ, branchID, int64(door))

	// Test getting the QR client
	qrClient, err := clientState.GetQRClient(clientID)
	assert.NoError(t, err)
	assert.NotNil(t, qrClient)
	assert.Equal(t, clientID, qrClient.Id)
	assert.Equal(t, typ, qrClient.CheckType())
	assert.Equal(t, branchID, qrClient.BranchId)
	assert.True(t, qrClient.CheckValid())
	assert.False(t, qrClient.CheckIsProcessing())
	assert.False(t, qrClient.CheckIsError())
}

func TestClientState_RemoveClients(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Create clients
	clientState.CreateDoorClient("door-1", 100, "Door 1", 1)
	clientState.CreateQRClient("qr-1", "in", 1, 100)

	// Verify they exist
	_, err := clientState.GetDoorClient("door-1")
	assert.NoError(t, err)
	_, err = clientState.GetQRClient("qr-1")
	assert.NoError(t, err)

	// Remove them
	clientState.RemoveDoorClient("door-1")
	clientState.RemoveQRClient("qr-1")

	// Verify they're gone
	_, err = clientState.GetDoorClient("door-1")
	assert.Error(t, err)
	_, err = clientState.GetQRClient("qr-1")
	assert.Error(t, err)
}

func TestClientState_GetByBranch(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Create clients in different branches
	clientState.CreateDoorClient("door-1", 100, "Door 1", 1)
	clientState.CreateDoorClient("door-2", 101, "Door 2", 1)
	clientState.CreateDoorClient("door-3", 102, "Door 3", 2)

	clientState.CreateQRClient("qr-1", "in", 1, 100)
	clientState.CreateQRClient("qr-2", "out", 1, 101)
	clientState.CreateQRClient("qr-3", "in", 2, 102)

	// Test getting by branch
	branch1Doors := clientState.GetDoorClientsByBranch(1)
	assert.Len(t, branch1Doors, 2)

	branch2Doors := clientState.GetDoorClientsByBranch(2)
	assert.Len(t, branch2Doors, 1)

	branch1QRs := clientState.GetQRClientsByBranch(1)
	assert.Len(t, branch1QRs, 2)

	branch2QRs := clientState.GetQRClientsByBranch(2)
	assert.Len(t, branch2QRs, 1)
}

func TestDoorClient_StateManagement(t *testing.T) {
	doorClient := wspkg.NewDoorClient(100, "Test Door", 1)

	// Test initial state
	assert.False(t, doorClient.IsOpen())
	assert.False(t, doorClient.CheckIsProcessing())
	assert.NotEmpty(t, doorClient.GetSecKey())

	// Test state changes
	doorClient.SetIsOpen(true)
	assert.True(t, doorClient.IsOpen())

	doorClient.SetIsProcessing(true)
	assert.True(t, doorClient.CheckIsProcessing())

	// Test time tracking
	now := time.Now()
	doorClient.SetLastCmd(now)
	assert.Equal(t, now, doorClient.GetLastCmd())

	// Test security key generation
	oldKey := doorClient.GetSecKey()
	doorClient.GenNewSecKey()
	newKey := doorClient.GetSecKey()
	assert.NotEqual(t, oldKey, newKey)
}

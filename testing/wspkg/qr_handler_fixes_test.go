package wspkg_test

import (
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	testutils "github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/utils"
	"github.com/stretchr/testify/assert"
)

// TestQRHandlerFixes_ComponentValidation tests the individual components that were fixed
func TestQRHandlerFixes_ComponentValidation(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Test 1: Token validation and removal
	t.Run("TokenValidationAndRemoval", func(t *testing.T) {
		token := "test-token"
		clientID := "qr-client-123"
		branchID := 1
		reqDeviceID := "100"
		
		// Add token
		setup.TokenStore.AddQRToken(token, clientID, "qr-in", branchID, reqDeviceID)
		
		// Validate token exists
		tokenData, valid := setup.TokenStore.ValidateToken(token)
		assert.True(t, valid)
		assert.Equal(t, "qr-in", tokenData.ConnType)
		assert.Equal(t, reqDeviceID, tokenData.ReqId)
		
		// Remove token (simulating what happens in the handler)
		setup.TokenStore.RemoveToken(token)
		
		// Verify token is removed
		_, valid = setup.TokenStore.ValidateToken(token)
		assert.False(t, valid)
	})
	
	// Test 2: Client state management and cleanup
	t.Run("ClientStateCleanup", func(t *testing.T) {
		clientState := wspkg.NewClientState()
		
		// Create door client
		clientState.CreateDoorClient("door-client-1", 100, "Test Door", 1)
		
		// Verify door client exists
		doorClient, err := clientState.GetDoorClient("door-client-1")
		assert.NoError(t, err)
		assert.Equal(t, "Test Door", doorClient.Name)
		
		// Create QR client
		clientState.CreateQRClient("qr-client-1", "qr-in", 1, 100)
		
		// Verify QR client exists
		qrClient, err := clientState.GetQRClient("qr-client-1")
		assert.NoError(t, err)
		assert.Equal(t, "qr-in", qrClient.CheckType())
		
		// Test cleanup - remove QR client
		clientState.RemoveQRClient("qr-client-1")
		
		// Verify QR client is removed
		_, err = clientState.GetQRClient("qr-client-1")
		assert.Error(t, err)
		
		// Test cleanup - remove door client
		clientState.RemoveDoorClient("door-client-1")
		
		// Verify door client is removed
		_, err = clientState.GetDoorClient("door-client-1")
		assert.Error(t, err)
	})
	
	// Test 3: Manager client operations
	t.Run("ManagerClientOperations", func(t *testing.T) {
		// Test initial state
		assert.Equal(t, 0, setup.Manager.GetClientCount())
		
		// Test client removal (should handle non-existent clients gracefully)
		err := setup.Manager.RemoveClient("non-existent-client")
		assert.NoError(t, err)
		
		// Verify debug log was created for non-existent client removal
		logs := setup.Logger.GetLogs()
		found := false
		for _, log := range logs {
			if log.Message == "Attempted to remove non-existent client" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected debug log for non-existent client removal")
	})
}

// TestQRHandlerFixes_ErrorHandling tests error handling scenarios
func TestQRHandlerFixes_ErrorHandling(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Test 1: Invalid token handling
	t.Run("InvalidTokenHandling", func(t *testing.T) {
		// Test with completely invalid token
		_, valid := setup.TokenStore.ValidateToken("invalid-token")
		assert.False(t, valid)
		
		// Test with expired token
		expiredToken := "expired-token"
		setup.TokenStore.AddExpiredToken(expiredToken, "client-1", "qr-in", 1)
		_, valid = setup.TokenStore.ValidateToken(expiredToken)
		assert.False(t, valid)
	})
	
	// Test 2: Device validation
	t.Run("DeviceValidation", func(t *testing.T) {
		clientState := wspkg.NewClientState()
		
		// Test getting non-existent door client by ID
		doorClient := clientState.GetDoorClientsByDoorId(999)
		assert.Nil(t, doorClient)
		
		// Create door client and verify retrieval
		clientState.CreateDoorClient("door-client-1", 100, "Test Door", 1)
		doorClient = clientState.GetDoorClientsByDoorId(100)
		assert.NotNil(t, doorClient)
		assert.Equal(t, "Test Door", doorClient.Name)
	})
	
	// Test 3: Connection type validation
	t.Run("ConnectionTypeValidation", func(t *testing.T) {
		clientState := wspkg.NewClientState()
		
		// Create door client
		clientState.CreateDoorClient("door-client-1", 100, "Test Door", 1)
		doorClient, err := clientState.GetDoorClient("door-client-1")
		assert.NoError(t, err)
		
		// Test QR-in validation
		assert.Nil(t, doorClient.GetQRIn()) // Should be nil initially
		
		// Create QR-in client
		clientState.CreateQRClient("qr-in-client", "qr-in", 1, 100)
		qrInClient, err := clientState.GetQRClient("qr-in-client")
		assert.NoError(t, err)
		
		// Set QR-in on door client
		err = doorClient.SetQRIn(qrInClient)
		assert.NoError(t, err)
		
		// Now QR-in should exist
		assert.NotNil(t, doorClient.GetQRIn())
		
		// Test QR-out validation
		assert.Nil(t, doorClient.GetQROut()) // Should be nil initially
		
		// Create QR-out client
		clientState.CreateQRClient("qr-out-client", "qr-out", 1, 100)
		qrOutClient, err := clientState.GetQRClient("qr-out-client")
		assert.NoError(t, err)
		
		// Set QR-out on door client
		err = doorClient.SetQROut(qrOutClient)
		assert.NoError(t, err)
		
		// Now QR-out should exist
		assert.NotNil(t, doorClient.GetQROut())
	})
}

// TestQRHandlerFixes_ConcurrencyAndRaceConditions tests thread safety
func TestQRHandlerFixes_ConcurrencyAndRaceConditions(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Test concurrent client state operations
	t.Run("ConcurrentClientStateOperations", func(t *testing.T) {
		clientState := wspkg.NewClientState()
		
		// Create door client
		clientState.CreateDoorClient("door-client-1", 100, "Test Door", 1)
		
		// Test concurrent QR client creation and removal
		numGoroutines := 10
		done := make(chan bool, numGoroutines)
		
		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				defer func() { done <- true }()
				
				clientID := "qr-client-" + string(rune('0'+index))
				
				// Create QR client
				clientState.CreateQRClient(clientID, "qr-in", 1, 100)
				
				// Brief pause
				time.Sleep(10 * time.Millisecond)
				
				// Remove QR client
				clientState.RemoveQRClient(clientID)
			}(i)
		}
		
		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			select {
			case <-done:
				// Goroutine completed
			case <-time.After(5 * time.Second):
				t.Fatal("Timeout waiting for concurrent operations")
			}
		}
		
		// Verify system is in clean state
		qrClients := clientState.GetQRClientsByBranch(1)
		assert.Len(t, qrClients, 0, "All QR clients should be cleaned up")
	})
	
	// Test concurrent manager operations
	t.Run("ConcurrentManagerOperations", func(t *testing.T) {
		numGoroutines := 5
		done := make(chan bool, numGoroutines)
		
		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				defer func() { done <- true }()
				
				clientID := "test-client-" + string(rune('0'+index))
				
				// Test concurrent client removal (should be safe)
				err := setup.Manager.RemoveClient(clientID)
				assert.NoError(t, err)
				
				// Test concurrent message broadcasting
				message := wspkg.NewMessage("test-event", []byte(`{"index": `+string(rune('0'+index))+`}`))
				setup.Manager.BroadcastMessage(message)
			}(i)
		}
		
		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			select {
			case <-done:
				// Goroutine completed
			case <-time.After(5 * time.Second):
				t.Fatal("Timeout waiting for concurrent manager operations")
			}
		}
		
		// Verify manager is in clean state
		assert.Equal(t, 0, setup.Manager.GetClientCount())
	})
}

// TestQRHandlerFixes_MemoryLeakPrevention tests memory leak prevention
func TestQRHandlerFixes_MemoryLeakPrevention(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Test multiple cycles of client creation and cleanup
	t.Run("MultipleCleanupCycles", func(t *testing.T) {
		clientState := wspkg.NewClientState()
		
		numCycles := 20
		
		for cycle := 0; cycle < numCycles; cycle++ {
			// Create door client
			doorClientID := "door-client-" + string(rune('0'+(cycle%10)))
			doorID := int64(100 + cycle)
			clientState.CreateDoorClient(doorClientID, doorID, "Test Door", 1)
			
			// Create multiple QR clients
			for qrIndex := 0; qrIndex < 3; qrIndex++ {
				qrClientID := "qr-client-" + string(rune('0'+cycle)) + "-" + string(rune('0'+qrIndex))
				qrType := "qr-in"
				if qrIndex%2 == 1 {
					qrType = "qr-out"
				}
				clientState.CreateQRClient(qrClientID, qrType, 1, doorID)
			}
			
			// Verify clients were created
			doorClient, err := clientState.GetDoorClient(doorClientID)
			assert.NoError(t, err)
			assert.Equal(t, doorID, doorClient.DoorId)
			
			// Clean up all QR clients
			qrClients := clientState.GetQRClientsByBranch(1)
			for _, qrClient := range qrClients {
				clientState.RemoveQRClient(qrClient.Id)
			}
			
			// Clean up door client
			clientState.RemoveDoorClient(doorClientID)
			
			// Verify cleanup
			_, err = clientState.GetDoorClient(doorClientID)
			assert.Error(t, err, "Door client should be removed")
			
			qrClients = clientState.GetQRClientsByBranch(1)
			assert.Len(t, qrClients, 0, "All QR clients should be removed")
		}
	})
	
	// Test token cleanup
	t.Run("TokenCleanup", func(t *testing.T) {
		numTokens := 50
		
		// Create many tokens
		for i := 0; i < numTokens; i++ {
			token := "test-token-" + string(rune('0'+(i%10)))
			clientID := "client-" + string(rune('0'+(i%10)))
			setup.TokenStore.AddQRToken(token, clientID, "qr-in", 1, "100")
		}
		
		// Verify tokens exist
		allTokens := setup.TokenStore.GetAllTokens()
		assert.GreaterOrEqual(t, len(allTokens), 1)
		
		// Clean up all tokens
		for token := range allTokens {
			setup.TokenStore.RemoveToken(token)
		}
		
		// Verify cleanup
		allTokens = setup.TokenStore.GetAllTokens()
		assert.Len(t, allTokens, 0, "All tokens should be removed")
	})
}

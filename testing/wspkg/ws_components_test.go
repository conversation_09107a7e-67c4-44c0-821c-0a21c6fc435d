package wspkg_test

import (
	"errors"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	testutils "github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/utils"
	"github.com/stretchr/testify/assert"
)

// TestWebSocketComponents tests the individual components that make up the WebSocket system
func TestWebSocketComponents_TokenValidation(t *testing.T) {
	setup := testutils.NewTestSetup(t)

	// Test valid token
	token := "valid-token"
	deviceID := "1"
	branchID := 1

	setup.TokenStore.AddDoorLockToken(token, deviceID, branchID)
	tokenData, valid := setup.TokenStore.ValidateToken(token)
	assert.True(t, valid)
	assert.Equal(t, deviceID, tokenData.ClientId)
	assert.Equal(t, "door-lock", tokenData.ConnType)
	assert.Equal(t, branchID, tokenData.BranchId)

	// Test invalid token
	_, valid = setup.TokenStore.ValidateToken("invalid-token")
	assert.False(t, valid)

	// Test expired token
	expiredToken := "expired-token"
	setup.TokenStore.AddExpiredToken(expiredToken, deviceID, "door-lock", branchID)
	_, valid = setup.TokenStore.ValidateToken(expiredToken)
	assert.False(t, valid)
}

func TestWebSocketComponents_DatabaseOperations(t *testing.T) {
	setup := testutils.NewTestSetup(t)

	// Test successful device data retrieval
	deviceID := int64(1)
	branchID := int64(1)
	deviceName := "Test Door Lock"

	setup.DB.SetValidDevice(deviceID, branchID, deviceName)
	deviceData, err := setup.DB.GetDeviceData(nil, deviceID)
	assert.NoError(t, err)
	assert.Equal(t, deviceID, deviceData.ID)
	assert.Equal(t, deviceName, deviceData.DeviceName)
	assert.Equal(t, branchID, deviceData.BranchID)

	// Test database error
	setup.DB.SetDatabaseError(errors.New("database connection failed"))
	_, err = setup.DB.GetDeviceData(nil, deviceID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database connection failed")
}

func TestWebSocketComponents_ClientStateManagement(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Test creating door client
	clientID := "test-client-1"
	doorID := int64(100)
	name := "Main Entrance"
	branchID := 1

	clientState.CreateDoorClient(clientID, doorID, name, branchID)

	// Test retrieving door client
	doorClient, err := clientState.GetDoorClient(clientID)
	assert.NoError(t, err)
	assert.NotNil(t, doorClient)
	assert.Equal(t, doorID, doorClient.DoorId)
	assert.Equal(t, name, doorClient.Name)
	assert.Equal(t, branchID, doorClient.BranchId)

	// Test door client state
	assert.False(t, doorClient.IsOpen())
	assert.False(t, doorClient.CheckIsProcessing())

	doorClient.SetIsOpen(true)
	assert.True(t, doorClient.IsOpen())

	// Test getting clients by branch
	clients := clientState.GetDoorClientsByBranch(branchID)
	assert.Len(t, clients, 1)
	assert.Equal(t, name, clients[0].Name)

	// Test removing client
	clientState.RemoveDoorClient(clientID)
	_, err = clientState.GetDoorClient(clientID)
	assert.Error(t, err)
	assert.Equal(t, "door client not found", err.Error())
}

func TestWebSocketComponents_QRClientManagement(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Test creating QR client
	clientID := "qr-client-1"
	typ := "in"
	branchID := 1
	door := 100

	clientState.CreateQRClient(clientID, typ, branchID, int64(door))

	// Test retrieving QR client
	qrClient, err := clientState.GetQRClient(clientID)
	assert.NoError(t, err)
	assert.NotNil(t, qrClient)
	assert.Equal(t, clientID, qrClient.Id)
	assert.Equal(t, typ, qrClient.CheckType())
	assert.Equal(t, branchID, qrClient.BranchId)
	assert.True(t, qrClient.CheckValid())

	// Test QR client state
	assert.False(t, qrClient.CheckIsProcessing())
	assert.False(t, qrClient.CheckIsError())

	qrClient.SetIsProcessing(true)
	assert.True(t, qrClient.CheckIsProcessing())

	// Test getting QR clients by branch
	qrClients := clientState.GetQRClientsByBranch(branchID)
	assert.Len(t, qrClients, 1)
	assert.Equal(t, clientID, qrClients[0].Id)

	// Test removing QR client
	clientState.RemoveQRClient(clientID)
	_, err = clientState.GetQRClient(clientID)
	assert.Error(t, err)
}

func TestWebSocketComponents_ManagerOperations(t *testing.T) {
	setup := testutils.NewTestSetup(t)

	// Test initial state
	assert.Equal(t, 0, setup.Manager.GetClientCount())

	// Test client retrieval (non-existent)
	client, exists := setup.Manager.GetClient("non-existent")
	assert.Nil(t, client)
	assert.False(t, exists)

	// Test removing non-existent client
	err := setup.Manager.RemoveClient("non-existent")
	assert.NoError(t, err)

	// Test broadcasting to empty manager
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	setup.Manager.BroadcastMessage(message)

	// Test group broadcasting
	group := []string{"client1", "client2"}
	setup.Manager.BroadcastMessageToGroup(message, group)

	// Verify debug logs were created
	logs := setup.Logger.GetLogs()
	assert.Greater(t, len(logs), 0)
}

func TestWebSocketComponents_ErrorHandling(t *testing.T) {
	setup := testutils.NewTestSetup(t)

	// Clear any existing logs from setup
	setup.Logger.Clear()

	// Test error logging
	setup.Logger.Error("Test error message")
	errorLogs := setup.Logger.GetErrorLogs()
	assert.Len(t, errorLogs, 1)
	assert.Equal(t, "Test error message", errorLogs[0].Message)

	// Test info logging
	setup.Logger.Info("Test info message")
	infoLogs := setup.Logger.GetInfoLogs()
	assert.Len(t, infoLogs, 1)
	assert.Equal(t, "Test info message", infoLogs[0].Message)

	// Test error checking
	assert.True(t, setup.Logger.HasErrorWithMessage("Test error message"))
	assert.False(t, setup.Logger.HasErrorWithMessage("Non-existent error"))
}

func TestWebSocketComponents_ConfigurationManagement(t *testing.T) {
	// Test default configuration
	config := wspkg.DefaultConfig()
	assert.Equal(t, 60*time.Second, config.ReadTimeout)
	assert.Equal(t, 10*time.Second, config.WriteTimeout)
	assert.Equal(t, 1000, config.MaxClients)
	assert.Equal(t, []string{"*"}, config.AllowedOrigins)

	// Test manager configuration
	setup := testutils.NewTestSetup(t)

	// Test setting allowed origins
	origins := []string{"http://localhost:3000", "https://example.com"}
	setup.Manager.SetAllowedOrigins(origins)

	// Verify info log was created
	infoLogs := setup.Logger.GetInfoLogs()
	found := false
	for _, log := range infoLogs {
		if log.Message == "Allowed origins updated" {
			found = true
			break
		}
	}
	assert.True(t, found)
}

func TestWebSocketComponents_MessageHandling(t *testing.T) {
	// Test message creation
	event := "test-event"
	data := []byte(`{"action": "click", "element": "button"}`)

	message := wspkg.NewMessage(event, data)
	assert.Equal(t, event, message.Event)
	assert.Equal(t, data, []byte(message.Data))
	assert.Equal(t, event, message.GetEvent())
	assert.Equal(t, data, message.GetData())

	// Test message from bytes
	validJSON := []byte(`{"event": "test-event", "data": {"key": "value"}}`)
	message2, err := wspkg.NewMessageFromBytes(validJSON)
	assert.NoError(t, err)
	assert.Equal(t, "test-event", message2.Event)
	assert.Equal(t, []byte(`{"key": "value"}`), []byte(message2.Data))

	// Test invalid JSON
	invalidJSON := []byte(`{"event": "test-event", "data": invalid}`)
	_, err = wspkg.NewMessageFromBytes(invalidJSON)
	assert.Error(t, err)
}

func TestWebSocketComponents_EventRouting(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Test registering and handling events
	handlerCalled := false
	testHandler := func(c wspkg.Ctx) error {
		handlerCalled = true
		return nil
	}

	router.On("test-event", testHandler)

	// Create context and handle event
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	ctx := wspkg.Ctx{Data: message}

	err := router.Handle(ctx)
	assert.NoError(t, err)
	assert.True(t, handlerCalled)

	// Test unknown event
	unknownMessage := wspkg.NewMessage("unknown-event", []byte(`{}`))
	unknownCtx := wspkg.Ctx{Data: unknownMessage}

	err = router.Handle(unknownCtx)
	assert.Error(t, err)
	assert.Equal(t, "event not found", err.Error())
}
